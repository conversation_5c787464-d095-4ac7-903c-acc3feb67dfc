#root {
  max-width: 100vw;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.gif-viewer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #1a1a1a;
  color: #ffffff;
}

.header {
  padding: 1rem;
  background: #2a2a2a;
  border-bottom: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #ffffff;
}

.file-input {
  padding: 0.5rem;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #ffffff;
  cursor: pointer;
}

.file-input:hover {
  background: #444;
}

.no-files {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.no-files p {
  margin: 0;
  font-size: 1.2rem;
}

.instructions {
  color: #888;
  font-size: 0.9rem;
  display: flex;
  gap: 2rem;
  justify-content: center;
  padding: 1rem;
}

.viewer-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.gif-display {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  overflow: hidden;
}

.gif-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.controls {
  padding: 1rem;
  background: #2a2a2a;
  border-top: 1px solid #444;
  text-align: center;
}

.status {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #61dafb;
}

.filename {
  font-size: 0.9rem;
  color: #ccc;
  word-break: break-all;
}