# GIF Viewer - Python Desktop Application

A simple, standalone desktop application for viewing GIF files in slideshow mode with keyboard navigation and file deletion capabilities.

## Features

- 🖼️ **GIF Slideshow**: View GIF files with full animation support
- ⌨️ **Keyboard Navigation**: Use arrow keys to navigate between GIFs
- 🗑️ **Recycle Bin**: Press spacebar to move current GIF to recycle bin
- 📊 **Status Display**: Shows current index (e.g., "3 of 15") and filename
- 📁 **Flexible Loading**: Load individual files or entire directories
- 🎨 **Clean Interface**: Dark theme with intuitive controls

## Installation

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Setup
1. Install the required dependency:
   ```bash
   pip install -r requirements.txt
   ```
   
   Or install Pillow directly:
   ```bash
   pip install Pillow
   ```

## Usage

### Running the Application
```bash
python gif_viewer.py
```

### Controls
- **← →** Arrow Keys: Navigate between GIFs (previous/next)
- **Spacebar**: Move current GIF to recycle bin (with confirmation)
- **Esc**: Quit the application

### Loading GIFs
1. **Load GIF Files**: Click to select individual GIF files
2. **Load Directory**: Click to load all GIF files from a folder
3. **Auto-load**: The app automatically loads GIF files from the current directory on startup

### File Operations
- **Navigation**: Cycles through GIFs (wraps around at beginning/end)
- **Recycle Bin**: Safely moves files to recycle bin (with confirmation dialog)
- **Error Handling**: Gracefully handles corrupted or invalid GIF files

## Technical Details

- **GUI Framework**: tkinter (built into Python)
- **Image Processing**: Pillow (PIL) for GIF handling and animation
- **File Operations**: send2trash for safe file removal to recycle bin
- **Animation**: Proper GIF frame timing and looping support
- **Responsive**: Automatically resizes large GIFs to fit the display

## File Structure
```
python_app/
├── gif_viewer.py          # Main application
├── requirements.txt       # Python dependencies (Pillow, send2trash)
├── README_PYTHON.md       # This file
├── run_gif_viewer.bat     # Windows launcher
└── run_gif_viewer.sh      # Unix/Linux launcher
```

## Advantages over Web Version

- ✅ **No localhost required**: Runs as a standalone desktop app
- ✅ **Safe file removal**: Moves files to recycle bin (recoverable)
- ✅ **Better performance**: Native desktop application
- ✅ **Offline usage**: No web server or internet connection needed
- ✅ **System integration**: Works with your file system directly

## Troubleshooting

### Common Issues

1. **"No module named 'PIL'"**
   - Solution: Install Pillow with `pip install Pillow`

2. **GIF not animating**
   - The GIF might be corrupted or have unusual formatting
   - Try with a different GIF file

3. **Permission denied when deleting**
   - Make sure you have write permissions to the directory
   - Close any other applications that might be using the file

### System Requirements
- **Windows**: Windows 7 or later
- **macOS**: macOS 10.12 or later  
- **Linux**: Most modern distributions with Python 3.7+

## License
This is a simple utility script - feel free to modify and use as needed.
