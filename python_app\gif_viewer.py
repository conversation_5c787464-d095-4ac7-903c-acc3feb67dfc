#!/usr/bin/env python3
"""
GIF Viewer - A simple desktop application for viewing GIF files in slideshow mode
Features:
- Arrow keys for navigation (left/right)
- Spacebar to delete current GIF file
- Shows current index and total count
- Displays filename
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import os
import glob
from pathlib import Path
from send2trash import send2trash


class GIFViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("GIF Viewer")
        self.root.geometry("1000x700")
        self.root.configure(bg='#1a1a1a')
        
        # State variables
        self.gif_files = []
        self.current_index = 0
        self.current_image = None
        self.animation_frames = []
        self.animation_index = 0
        self.animation_job = None
        
        # Create GUI
        self.create_widgets()
        self.bind_keys()
        
        # Load GIFs from current directory on startup
        self.load_gifs_from_directory()
    
    def create_widgets(self):
        """Create the GUI widgets"""
        # Header frame
        header_frame = tk.Frame(self.root, bg='#2a2a2a', height=60)
        header_frame.pack(fill='x', padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)
        
        # Title and controls
        title_label = tk.Label(header_frame, text="GIF Viewer", 
                              font=('Arial', 16, 'bold'), 
                              fg='white', bg='#2a2a2a')
        title_label.pack(side='left', padx=10, pady=15)
        
        # Load button
        load_btn = tk.Button(header_frame, text="Load GIF Files", 
                           command=self.load_gif_files,
                           bg='#61dafb', fg='#1a1a1a', 
                           font=('Arial', 10, 'bold'),
                           padx=15, pady=5)
        load_btn.pack(side='right', padx=10, pady=15)
        
        # Load directory button
        load_dir_btn = tk.Button(header_frame, text="Load Directory", 
                               command=self.load_directory,
                               bg='#4fa8c5', fg='#1a1a1a', 
                               font=('Arial', 10, 'bold'),
                               padx=15, pady=5)
        load_dir_btn.pack(side='right', padx=(0, 10), pady=15)
        
        # Main display area
        self.display_frame = tk.Frame(self.root, bg='#1a1a1a')
        self.display_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Image label
        self.image_label = tk.Label(self.display_frame, bg='#1a1a1a', 
                                   text="No GIF files loaded\n\nClick 'Load GIF Files' or 'Load Directory' to start",
                                   fg='white', font=('Arial', 14))
        self.image_label.pack(expand=True)
        
        # Status frame
        status_frame = tk.Frame(self.root, bg='#2a2a2a', height=80)
        status_frame.pack(fill='x', padx=10, pady=(0, 10))
        status_frame.pack_propagate(False)
        
        # Status info
        self.status_label = tk.Label(status_frame, text="0 of 0", 
                                    font=('Arial', 14, 'bold'), 
                                    fg='#61dafb', bg='#2a2a2a')
        self.status_label.pack(pady=(10, 5))
        
        self.filename_label = tk.Label(status_frame, text="", 
                                      font=('Arial', 10), 
                                      fg='#ccc', bg='#2a2a2a')
        self.filename_label.pack(pady=(0, 5))
        
        # Instructions
        instructions = tk.Label(status_frame,
                               text="← → Navigate    Space: Move to Recycle Bin    Esc: Quit",
                               font=('Arial', 9),
                               fg='#888', bg='#2a2a2a')
        instructions.pack(pady=(0, 10))
    
    def bind_keys(self):
        """Bind keyboard events"""
        self.root.bind('<Key>', self.on_key_press)
        self.root.focus_set()  # Make sure the window can receive key events
    
    def on_key_press(self, event):
        """Handle keyboard input"""
        if event.keysym == 'Left':
            self.previous_gif()
        elif event.keysym == 'Right':
            self.next_gif()
        elif event.keysym == 'space':
            self.delete_current_gif()
        elif event.keysym == 'Escape':
            self.root.quit()
    
    def load_gif_files(self):
        """Load GIF files using file dialog"""
        file_types = [
            ('GIF files', '*.gif'),
            ('All files', '*.*')
        ]
        
        files = filedialog.askopenfilenames(
            title="Select GIF files",
            filetypes=file_types
        )
        
        if files:
            # Filter to only GIF files
            self.gif_files = [f for f in files if f.lower().endswith('.gif')]
            self.current_index = 0
            self.update_display()
    
    def load_directory(self):
        """Load all GIF files from a selected directory"""
        directory = filedialog.askdirectory(title="Select directory containing GIF files")
        
        if directory:
            self.load_gifs_from_path(directory)
    
    def load_gifs_from_directory(self):
        """Load GIF files from current directory"""
        current_dir = os.getcwd()
        self.load_gifs_from_path(current_dir)
    
    def load_gifs_from_path(self, path):
        """Load all GIF files from the specified path"""
        gif_pattern = os.path.join(path, "*.gif")
        self.gif_files = glob.glob(gif_pattern, recursive=False)
        self.gif_files.sort()  # Sort alphabetically
        self.current_index = 0
        self.update_display()
        
        if self.gif_files:
            print(f"Loaded {len(self.gif_files)} GIF files from {path}")
        else:
            print(f"No GIF files found in {path}")
    
    def update_display(self):
        """Update the display with current GIF"""
        if not self.gif_files:
            self.image_label.configure(image='', 
                                     text="No GIF files loaded\n\nClick 'Load GIF Files' or 'Load Directory' to start")
            self.status_label.configure(text="0 of 0")
            self.filename_label.configure(text="")
            return
        
        # Update status
        self.status_label.configure(text=f"{self.current_index + 1} of {len(self.gif_files)}")
        filename = os.path.basename(self.gif_files[self.current_index])
        self.filename_label.configure(text=filename)
        
        # Load and display the GIF
        self.load_gif_animation()
    
    def load_gif_animation(self):
        """Load GIF animation frames"""
        if self.animation_job:
            self.root.after_cancel(self.animation_job)
        
        try:
            gif_path = self.gif_files[self.current_index]
            image = Image.open(gif_path)
            
            # Extract all frames
            self.animation_frames = []
            self.animation_index = 0
            
            try:
                while True:
                    # Resize frame to fit display while maintaining aspect ratio
                    frame = image.copy()
                    frame = self.resize_image(frame)
                    photo = ImageTk.PhotoImage(frame)
                    
                    # Get frame duration (default 100ms if not specified)
                    duration = image.info.get('duration', 100)
                    self.animation_frames.append((photo, duration))
                    
                    image.seek(image.tell() + 1)
            except EOFError:
                pass  # End of frames
            
            if self.animation_frames:
                self.animate_gif()
            
        except Exception as e:
            self.image_label.configure(image='', text=f"Error loading GIF:\n{str(e)}")
    
    def resize_image(self, image):
        """Resize image to fit display area while maintaining aspect ratio"""
        # Get display area size (approximate)
        max_width = 800
        max_height = 500
        
        # Calculate scaling factor
        width_ratio = max_width / image.width
        height_ratio = max_height / image.height
        scale_factor = min(width_ratio, height_ratio, 1.0)  # Don't upscale
        
        if scale_factor < 1.0:
            new_width = int(image.width * scale_factor)
            new_height = int(image.height * scale_factor)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        return image
    
    def animate_gif(self):
        """Animate the GIF by cycling through frames"""
        if not self.animation_frames:
            return
        
        photo, duration = self.animation_frames[self.animation_index]
        self.image_label.configure(image=photo, text='')
        
        # Move to next frame
        self.animation_index = (self.animation_index + 1) % len(self.animation_frames)
        
        # Schedule next frame
        self.animation_job = self.root.after(duration, self.animate_gif)
    
    def previous_gif(self):
        """Go to previous GIF"""
        if self.gif_files:
            self.current_index = (self.current_index - 1) % len(self.gif_files)
            self.update_display()
    
    def next_gif(self):
        """Go to next GIF"""
        if self.gif_files:
            self.current_index = (self.current_index + 1) % len(self.gif_files)
            self.update_display()
    
    def delete_current_gif(self):
        """Move the current GIF file to recycle bin"""
        if not self.gif_files:
            return

        current_file = self.gif_files[self.current_index]
        filename = os.path.basename(current_file)

        # Confirm deletion
        result = messagebox.askyesno(
            "Move to Recycle Bin",
            f"Are you sure you want to move '{filename}' to the recycle bin?",
            icon='question'
        )

        if result:
            try:
                # Stop animation
                if self.animation_job:
                    self.root.after_cancel(self.animation_job)

                # Move file to recycle bin
                send2trash(current_file)

                # Remove from list
                self.gif_files.pop(self.current_index)

                # Adjust current index
                if not self.gif_files:
                    self.current_index = 0
                elif self.current_index >= len(self.gif_files):
                    self.current_index = len(self.gif_files) - 1

                # Update display
                self.update_display()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to move file to recycle bin:\n{str(e)}")


def main():
    """Main function to run the GIF viewer"""
    root = tk.Tk()
    app = GIFViewer(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\nGIF Viewer closed.")


if __name__ == "__main__":
    main()
