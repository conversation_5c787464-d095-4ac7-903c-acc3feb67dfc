import { useState, useEffect, useCallback } from 'react'
import './App.css'

// Extended File interface to store file handle for deletion
interface ExtendedFile extends File {
  handle?: FileSystemFileHandle
}

function App() {
  const [gifFiles, setGifFiles] = useState<ExtendedFile[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [currentGifUrl, setCurrentGifUrl] = useState<string>('')
  const [supportsFileSystemAccess, setSupportsFileSystemAccess] = useState(false)
  const [deleteStatus, setDeleteStatus] = useState<string>('')

  // Check if File System Access API is supported
  useEffect(() => {
    setSupportsFileSystemAccess('showOpenFilePicker' in window)
  }, [])

  // Load GIF files using File System Access API
  const handleFileSystemAccess = useCallback(async () => {
    try {
      if (!('showOpenFilePicker' in window)) {
        alert('File System Access API not supported in this browser. Please use Chrome/Edge.')
        return
      }

      const fileHandles = await (window as any).showOpenFilePicker({
        multiple: true,
        types: [{
          description: 'GIF files',
          accept: {
            'image/gif': ['.gif']
          }
        }]
      })

      const files: ExtendedFile[] = []
      for (const handle of fileHandles) {
        const file = await handle.getFile()
        if (file.type === 'image/gif' || file.name.toLowerCase().endsWith('.gif')) {
          // Store the file handle for deletion
          const extendedFile = file as ExtendedFile
          extendedFile.handle = handle
          files.push(extendedFile)
        }
      }

      setGifFiles(files)
      setCurrentIndex(0)
      setDeleteStatus('')
    } catch (error) {
      if ((error as Error).name !== 'AbortError') {
        console.error('Error accessing files:', error)
        alert('Error accessing files. Please try again.')
      }
    }
  }, [])

  // Load GIF files from file input (fallback)
  const handleFileInput = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      const gifFiles = Array.from(files).filter(file =>
        file.type === 'image/gif' || file.name.toLowerCase().endsWith('.gif')
      ) as ExtendedFile[]
      setGifFiles(gifFiles)
      setCurrentIndex(0)
      setDeleteStatus('Note: Files loaded via input cannot be deleted from disk.')
    }
  }, [])

  // Update current GIF URL when index changes
  useEffect(() => {
    if (gifFiles.length > 0 && gifFiles[currentIndex]) {
      const url = URL.createObjectURL(gifFiles[currentIndex])
      setCurrentGifUrl(url)

      // Cleanup previous URL
      return () => {
        URL.revokeObjectURL(url)
      }
    }
  }, [gifFiles, currentIndex])

  // Navigate to previous GIF
  const goToPrevious = useCallback(() => {
    if (gifFiles.length > 0) {
      setCurrentIndex(prev => prev > 0 ? prev - 1 : gifFiles.length - 1)
    }
  }, [gifFiles.length])

  // Navigate to next GIF
  const goToNext = useCallback(() => {
    if (gifFiles.length > 0) {
      setCurrentIndex(prev => prev < gifFiles.length - 1 ? prev + 1 : 0)
    }
  }, [gifFiles.length])

  // Delete current GIF from disk and slideshow
  const deleteCurrent = useCallback(async () => {
    if (gifFiles.length === 0) return

    const currentFile = gifFiles[currentIndex]
    const fileName = currentFile.name

    try {
      // Try to delete from file system if we have a handle
      if (currentFile.handle) {
        const confirmed = confirm(`Are you sure you want to permanently delete "${fileName}" from your computer?`)
        if (!confirmed) return

        await currentFile.handle.remove()
        setDeleteStatus(`✓ Deleted "${fileName}" from disk`)
      } else {
        setDeleteStatus(`⚠ Cannot delete "${fileName}" from disk - file loaded via input`)
      }
    } catch (error) {
      console.error('Error deleting file:', error)
      setDeleteStatus(`✗ Failed to delete "${fileName}" from disk: ${(error as Error).message}`)
    }

    // Remove from slideshow regardless of disk deletion success
    const newFiles = gifFiles.filter((_, index) => index !== currentIndex)
    setGifFiles(newFiles)

    if (newFiles.length === 0) {
      setCurrentIndex(0)
      setCurrentGifUrl('')
    } else if (currentIndex >= newFiles.length) {
      setCurrentIndex(newFiles.length - 1)
    }

    // Clear status after 3 seconds
    setTimeout(() => setDeleteStatus(''), 3000)
  }, [gifFiles, currentIndex])

  // Keyboard event handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          goToPrevious()
          break
        case 'ArrowRight':
          event.preventDefault()
          goToNext()
          break
        case ' ':
          event.preventDefault()
          deleteCurrent()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [goToPrevious, goToNext, deleteCurrent])

  return (
    <div className="gif-viewer">
      <div className="header">
        <h1>GIF Viewer</h1>
        <div className="file-controls">
          {supportsFileSystemAccess ? (
            <button onClick={handleFileSystemAccess} className="file-button">
              Select GIF Files (with delete support)
            </button>
          ) : (
            <input
              type="file"
              multiple
              accept=".gif,image/gif"
              onChange={handleFileInput}
              className="file-input"
            />
          )}
        </div>
      </div>

      {gifFiles.length === 0 ? (
        <div className="no-files">
          <p>Select GIF files to start viewing</p>
          <p className="instructions">
            Use ← → arrow keys to navigate, spacebar to delete
          </p>
        </div>
      ) : (
        <div className="viewer-container">
          <div className="gif-display">
            <img
              src={currentGifUrl}
              alt={`GIF ${currentIndex + 1}`}
              className="gif-image"
            />
          </div>

          <div className="controls">
            <div className="status">
              {currentIndex + 1} of {gifFiles.length}
            </div>
            <div className="filename">
              {gifFiles[currentIndex]?.name}
            </div>
            {deleteStatus && (
              <div className={`delete-status ${deleteStatus.startsWith('✓') ? 'success' : deleteStatus.startsWith('✗') ? 'error' : 'warning'}`}>
                {deleteStatus}
              </div>
            )}
          </div>

          <div className="instructions">
            <span>← → Navigate</span>
            <span>Space: Delete {supportsFileSystemAccess && gifFiles[currentIndex]?.handle ? '(from disk)' : '(from slideshow)'}</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default App
