import { useState, useEffect, useCallback } from 'react'
import './App.css'

function App() {
  const [gifFiles, setGifFiles] = useState<File[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [currentGifUrl, setCurrentGifUrl] = useState<string>('')

  // Load GIF files from file input
  const handleFileInput = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      const gifFiles = Array.from(files).filter(file =>
        file.type === 'image/gif' || file.name.toLowerCase().endsWith('.gif')
      )
      setGifFiles(gifFiles)
      setCurrentIndex(0)
    }
  }, [])

  // Update current GIF URL when index changes
  useEffect(() => {
    if (gifFiles.length > 0 && gifFiles[currentIndex]) {
      const url = URL.createObjectURL(gifFiles[currentIndex])
      setCurrentGifUrl(url)

      // Cleanup previous URL
      return () => {
        URL.revokeObjectURL(url)
      }
    }
  }, [gifFiles, currentIndex])

  // Navigate to previous GIF
  const goToPrevious = useCallback(() => {
    if (gifFiles.length > 0) {
      setCurrentIndex(prev => prev > 0 ? prev - 1 : gifFiles.length - 1)
    }
  }, [gifFiles.length])

  // Navigate to next GIF
  const goToNext = useCallback(() => {
    if (gifFiles.length > 0) {
      setCurrentIndex(prev => prev < gifFiles.length - 1 ? prev + 1 : 0)
    }
  }, [gifFiles.length])

  // Delete current GIF
  const deleteCurrent = useCallback(() => {
    if (gifFiles.length > 0) {
      const newFiles = gifFiles.filter((_, index) => index !== currentIndex)
      setGifFiles(newFiles)

      if (newFiles.length === 0) {
        setCurrentIndex(0)
        setCurrentGifUrl('')
      } else if (currentIndex >= newFiles.length) {
        setCurrentIndex(newFiles.length - 1)
      }
    }
  }, [gifFiles, currentIndex])

  // Keyboard event handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          goToPrevious()
          break
        case 'ArrowRight':
          event.preventDefault()
          goToNext()
          break
        case ' ':
          event.preventDefault()
          deleteCurrent()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [goToPrevious, goToNext, deleteCurrent])

  return (
    <div className="gif-viewer">
      <div className="header">
        <h1>GIF Viewer</h1>
        <input
          type="file"
          multiple
          accept=".gif,image/gif"
          onChange={handleFileInput}
          className="file-input"
        />
      </div>

      {gifFiles.length === 0 ? (
        <div className="no-files">
          <p>Select GIF files to start viewing</p>
          <p className="instructions">
            Use ← → arrow keys to navigate, spacebar to delete
          </p>
        </div>
      ) : (
        <div className="viewer-container">
          <div className="gif-display">
            <img
              src={currentGifUrl}
              alt={`GIF ${currentIndex + 1}`}
              className="gif-image"
            />
          </div>

          <div className="controls">
            <div className="status">
              {currentIndex + 1} of {gifFiles.length}
            </div>
            <div className="filename">
              {gifFiles[currentIndex]?.name}
            </div>
          </div>

          <div className="instructions">
            <span>← → Navigate</span>
            <span>Space: Delete</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default App
